{"project_info": {"project_number": "1095286986673", "firebase_url": "https://saasdev-56bf0.firebaseio.com", "project_id": "saasdev-56bf0", "storage_bucket": "saasdev-56bf0.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "1:1095286986673:android:975d7a5ff3504cabe14f90", "android_client_info": {"package_name": "and.perksense.com.brand"}}, "oauth_client": [{"client_id": "1095286986673-4dlr8mag08liisagju1889jvp68kevt6.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyCddPrFPvjbb5YerCEe5qgbf_JGDDbbBPY"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "1095286986673-4dlr8mag08liisagju1889jvp68kevt6.apps.googleusercontent.com", "client_type": 3}, {"client_id": "1095286986673-0sjbcqh8daf5nletqmlrt8jsiluc5786.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.perksense.ios.perksense"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:1095286986673:android:d0c0c7c1be2f8f4de14f90", "android_client_info": {"package_name": "com.mykoodoo.pro"}}, "oauth_client": [{"client_id": "1095286986673-4dlr8mag08liisagju1889jvp68kevt6.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyCddPrFPvjbb5YerCEe5qgbf_JGDDbbBPY"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "1095286986673-4dlr8mag08liisagju1889jvp68kevt6.apps.googleusercontent.com", "client_type": 3}, {"client_id": "1095286986673-0sjbcqh8daf5nletqmlrt8jsiluc5786.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.perksense.ios.perksense"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:1095286986673:android:de3a03d4a6ed8c8ce14f90", "android_client_info": {"package_name": "com.mykoodoo.user"}}, "oauth_client": [{"client_id": "1095286986673-4dlr8mag08liisagju1889jvp68kevt6.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyCddPrFPvjbb5YerCEe5qgbf_JGDDbbBPY"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "1095286986673-4dlr8mag08liisagju1889jvp68kevt6.apps.googleusercontent.com", "client_type": 3}, {"client_id": "1095286986673-0sjbcqh8daf5nletqmlrt8jsiluc5786.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.perksense.ios.perksense"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:1095286986673:android:914785047b7cc866e14f90", "android_client_info": {"package_name": "com.perksense.and"}}, "oauth_client": [{"client_id": "1095286986673-4dlr8mag08liisagju1889jvp68kevt6.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyCddPrFPvjbb5YerCEe5qgbf_JGDDbbBPY"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "1095286986673-4dlr8mag08liisagju1889jvp68kevt6.apps.googleusercontent.com", "client_type": 3}, {"client_id": "1095286986673-0sjbcqh8daf5nletqmlrt8jsiluc5786.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.perksense.ios.perksense"}}]}}}], "configuration_version": "1"}